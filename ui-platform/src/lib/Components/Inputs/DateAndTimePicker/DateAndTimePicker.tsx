import { useEffect, useRef, useState } from 'react';
import { DateRange } from 'react-day-picker';
import {
  Control,
  Controller,
  FieldError,
  RegisterOptions,
} from 'react-hook-form';
import styled, { css } from 'styled-components';
import { Icon, IconTypes } from '../../Icons';
import { DatePicker } from '../DatepickerInput/Datepicker/DatePicker';
import { TimePickerV2 } from '../TimePickerV2/TimePickerV2';

type Props = {
  name: string;
  placeholder?: string;
  icon?: IconTypes;
  className?: string;
  iconPosition?: 'left' | 'right' | 'none';
  fieldError?: FieldError | undefined | null;
  rules?: RegisterOptions;
  control?: Control;
  label?: string;
  instructions?: string;
  value?: string; // ISO date string with time
  state?: 'default' | 'display-only';
  onChange?: (value: string) => void;
  validMinutes?: number[];
  // DatePicker specific props
  mode?: 'single'; // Only single mode supported for date-time picker
  numberOfMonths?: number;
  disablePast?: boolean;
  disabledDates?: string[];
  disableFuture?: boolean;
  weekendSelectable?: boolean;
};

const Container = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${(props) => props.theme.SpacingXs};
  transition: color 1s ease, background-color 1s ease;
  position: relative;
  width: 100%;
`;

const InputsContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr auto;
  gap: ${(props) => props.theme.SpacingXs};
  align-items: start;
`;

const InputContainer = styled.div`
  height: auto;
  min-height: 37px;
  display: grid;
  align-items: center;
  justify-content: center;
  min-width: 95px;
  cursor: pointer;
  grid-template-columns: 1fr;
  position: relative;
`;

const ActiveZone = styled.div`
  display: grid;
  grid-template-rows: 1fr auto;
  gap: ${(props) => props.theme.SpacingSm};
  align-items: self-start;
  width: 100%;
`;

const DateTimeInputContainer = styled.div<{
  dropdownOpen: boolean;
  state: 'default' | 'display-only';
  error?: boolean;
}>`
  height: 37px;
  overflow: hidden;
  z-index: 8;
  outline: unset;
  text-align: left;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border: 1px solid
    ${(props) =>
      props?.dropdownOpen
        ? props.theme.ColorsStrokesFocus
        : props.error
        ? props.theme.ColorsUtilityColorError
        : props.theme.ColorsStrokesGrey};
  box-sizing: border-box;
  width: 100%;
  padding: ${(props) => props.theme.SpacingSm};
  border-radius: ${(props) => props.theme.RadiusXs};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: grid;
  grid-template-areas: 'left-icon datetime-display right-icon dropdown-icon';
  grid-template-rows: 1fr;
  grid-template-columns: auto 1fr auto auto;
  align-content: center;

  ${({ state, dropdownOpen }) =>
    state === 'display-only'
      ? !dropdownOpen
        ? css`
            border: 1px solid ${(props) => props.theme.ColorsControllersDefault};
            background: ${(props) => props.theme.ColorsInputsNonEditable};
          `
        : css`
            background: ${(props) => props.theme.ColorsInputsPrimary};
            border: 1px solid ${(props) => props.theme.ColorsStrokesFocus};
          `
      : ''}
`;

const DateTimeInput = styled.input<{ dropdownOpen: boolean }>`
  all: unset;
  overflow: hidden;
  outline: unset;
  background-color: transparent;
  text-align: left;
  box-sizing: border-box;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;

  ::placeholder {
    color: ${(props) => props.theme.ColorsTypographyPrimary};
    transition: color 0.3s ease;
  }
`;

const DropdownIcon = styled(Icon)`
  grid-area: dropdown-icon;
  padding: 0 ${(props) => props.theme.SpacingXs};
`;

const DropdownMenu = styled.div<{ width: number }>`
  margin: 0 !important;
  position: absolute;
  right: 0px;
  left: 0px;
  border-radius: ${(props) => props.theme.RadiusXs};
  background-color: ${(props) => props.theme.ColorsOverlaySurfaceOverlay};
  border: 1px solid ${(props) => props.theme.ColorsStrokesFocus};
  box-sizing: border-box;
  overflow: hidden;
  display: grid;
  grid-template-rows: auto auto;
  gap: ${(props) => props.theme.SpacingSm};
  justify-items: center;
  text-align: self-start;
  color: ${(props) => props.theme.ColorsIconColorTertiary};
  padding: ${(props) => props.theme.SpacingSm};
  z-index: 1;
  width: 100%;

  div {
    color: ${(props) => props.theme.ColorsTypographyPrimary};
    align-items: center;
    outline: none;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`;

const TimePickerContainer = styled.div`
  width: 100%;
  border-top: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  padding-top: ${(props) => props.theme.SpacingSm};
`;

const StyledInstuctionContainer = styled.div`
  margin: unset;
`;

const Instruction = styled.div<{ error?: boolean }>`
  margin-top: ${(props) => props.theme.SpacingXs};
  color: ${({ error, theme }) =>
    error ? theme.ColorsUtilityColorError : theme.ColorsTypographySecondary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize2}px;
`;

const IconWrapper = styled(Icon)`
  display: grid;
  grid-auto-flow: column;
  place-items: center;
  align-self: center;
`;

const Label = styled.label`
  margin-bottom: ${(props) => props.theme.SpacingXs};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
`;

const LeftIconContainer = styled.span<{ position: 'left' | 'right' | 'none' }>`
  grid-area: left-icon;
  padding: 0
    ${(props) => (props?.position === 'left' ? props.theme.SpacingXs : 0)};
`;

const RightIconContainer = styled.span<{ position: 'left' | 'right' | 'none' }>`
  grid-area: right-icon;
  padding: 0
    ${(props) => (props?.position === 'right' ? props.theme.SpacingXs : 0)};
`;

/**
 * A combined date and time picker component that allows users to select both date and time.
 * The component manages a single ISO date string value that includes both date and time.
 *
 * @param {object} props - The component props.
 * @param {string} props.name - The input name for react-hook-form.
 * @param {string} [props.placeholder] - The input placeholder text.
 * @param {string} [props.datePlaceholder] - Specific placeholder for date part.
 * @param {string} [props.timePlaceholder] - Specific placeholder for time part.
 * @param {Control} props.control - The react-hook-form control.
 * @param {RegisterOptions} [props.rules] - Validation rules.
 * @param {FieldError} [props.fieldError] - Field error object.
 * @param {string} [props.label] - The input label.
 * @param {string} [props.instructions] - The input instructions.
 * @param {string} [props.value] - Default ISO date string value.
 * @param {function} [props.onChange] - Callback when value changes.
 * @param {number[]} [props.validMinutes] - Valid minutes for time picker.
 * @param {IconTypes} [props.icon] - Icon to display.
 * @param {'left' | 'right' | 'none'} [props.iconPosition] - Icon position.
 * @param {'default' | 'display-only'} [props.state] - Component state.
 * @param {number} [props.numberOfMonths] - Number of months to display in date picker.
 * @param {boolean} [props.disablePast] - Whether to disable past dates.
 * @param {string[]} [props.disabledDates] - Array of disabled dates.
 * @param {boolean} [props.disableFuture] - Whether to disable future dates.
 * @param {boolean} [props.weekendSelectable] - Whether weekends are selectable.
 * @return {JSX.Element} The date and time picker component.
 */
export const DateAndTimePicker = ({
  name,
  placeholder = 'Select date and time',
  icon,
  iconPosition: position = 'none',
  fieldError,
  rules,
  control,
  label,
  instructions,
  value,
  state = 'default',
  onChange,
  validMinutes = [0, 15, 30, 45],
  // DatePicker props
  mode = 'single',
  numberOfMonths = 1,
  disablePast = true,
  disabledDates = [],
  disableFuture = false,
  weekendSelectable = false,
}: Props) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [selectedTime, setSelectedTime] = useState<string>('');

  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputContainerRef = useRef<HTMLDivElement>(null);

  // Parse initial value
  useEffect(() => {
    if (value) {
      try {
        const date = new Date(value);
        if (!isNaN(date.getTime())) {
          setSelectedDate(date);
          const hours = date.getHours().toString().padStart(2, '0');
          const minutes = date.getMinutes().toString().padStart(2, '0');
          setSelectedTime(`${hours}:${minutes}`);
        }
      } catch (error) {
        console.warn('Invalid date value provided:', value);
      }
    }
  }, [value]);

  useEffect(() => {
    const updateDropdownWidth = () => {
      if (inputContainerRef.current) {
        const width = inputContainerRef.current.offsetWidth;
        if (dropdownRef.current) {
          dropdownRef.current.style.width = `${width}px`;
        }
      }
    };

    updateDropdownWidth();
    window.addEventListener('resize', updateDropdownWidth);

    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputContainerRef.current &&
        !inputContainerRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      window.removeEventListener('resize', updateDropdownWidth);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const formatDisplayValue = (date: Date | undefined, time: string) => {
    if (!date) return '';
    const dateStr = date.toDateString();
    return time ? `${dateStr} ${time}` : dateStr;
  };

  const createDateTimeString = (date: Date | undefined, time: string) => {
    if (!date || !time) return '';

    const [hours, minutes] = time.split(':').map(Number);
    const newDate = new Date(date);
    newDate.setHours(hours, minutes, 0, 0);
    return newDate.toISOString();
  };

  const handleInputClick = () => {
    setDropdownOpen((prev) => !prev);
  };

  const handleDateSelect = (date?: Date | DateRange | Date[]) => {
    if (date && date instanceof Date) {
      setSelectedDate(date);
    }
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({
        field: { onChange: onFieldChange, value: fieldValue },
        fieldState: { error = fieldError },
      }) => {
        // Update form value when date or time changes
        useEffect(() => {
          const dateTimeString = createDateTimeString(
            selectedDate,
            selectedTime
          );
          if (dateTimeString && dateTimeString !== fieldValue) {
            onFieldChange(dateTimeString);
            if (onChange) {
              onChange(dateTimeString);
            }
          }
        }, [selectedDate, selectedTime, onFieldChange, fieldValue, onChange]);

        return (
          <Container data-testid="date-time-picker">
            {!!label && <Label>{label}</Label>}
            <InputsContainer>
              <InputContainer data-testid="date-time-picker-wrapper">
                <ActiveZone
                  data-testid="date-time-picker-active-zone"
                  ref={inputContainerRef}
                >
                  <DateTimeInputContainer
                    data-testid="date-time-picker-container"
                    dropdownOpen={dropdownOpen}
                    onClick={handleInputClick}
                    state={state}
                    error={!!error?.message}
                  >
                    <LeftIconContainer
                      data-testid="date-time-picker-left-icon"
                      position={position}
                    >
                      {position === 'left' && !!icon && (
                        <IconWrapper type={icon} width={24} height={24} />
                      )}
                    </LeftIconContainer>
                    <DateTimeInput
                      dropdownOpen={dropdownOpen}
                      type="text"
                      placeholder={
                        fieldValue
                          ? formatDisplayValue(selectedDate, selectedTime)
                          : placeholder
                      }
                      name={name}
                      readOnly
                    />
                    <RightIconContainer position={position}>
                      {position === 'right' && !!icon && (
                        <IconWrapper type={icon} width={24} height={24} />
                      )}
                    </RightIconContainer>
                    <DropdownIcon
                      type={dropdownOpen ? 'chevron-up' : 'chevron-down'}
                    />
                  </DateTimeInputContainer>
                  <div>
                    {dropdownOpen && (
                      <DropdownMenu
                        data-testid="date-time-picker-dropdown"
                        width={inputContainerRef.current?.offsetWidth || 0}
                        ref={dropdownRef}
                      >
                        <DatePicker
                          data-testid="date-picker"
                          mode={mode}
                          numberOfMonths={numberOfMonths}
                          disablePast={disablePast}
                          disabledDates={disabledDates}
                          disableFuture={disableFuture}
                          weekendSelectable={weekendSelectable}
                          selected={selectedDate}
                          onSelect={handleDateSelect}
                        />
                        <TimePickerContainer>
                          <TimePickerV2
                            data-testid="time-picker"
                            onSelect={handleTimeSelect}
                            selected={selectedTime}
                            validMinutes={validMinutes}
                          />
                        </TimePickerContainer>
                      </DropdownMenu>
                    )}
                  </div>
                </ActiveZone>
              </InputContainer>
            </InputsContainer>
            {error ? (
              <Instruction error={!!error?.message}>
                {error?.message}
              </Instruction>
            ) : (
              <StyledInstuctionContainer>
                {instructions && <Instruction>{instructions}</Instruction>}
              </StyledInstuctionContainer>
            )}
          </Container>
        );
      }}
    />
  );
};
