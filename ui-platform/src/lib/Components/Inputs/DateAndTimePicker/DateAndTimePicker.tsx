import { useEffect, useRef, useState } from 'react';
import { DateRange } from 'react-day-picker';
import {
  Control,
  Controller,
  FieldError,
  RegisterOptions,
} from 'react-hook-form';
import styled, { css } from 'styled-components';
import { Icon, IconTypes } from '../../Icons';
import { DatePicker } from '../DatepickerInput/Datepicker/DatePicker';
import { TimePickerV2 } from '../TimePickerV2/TimePickerV2';

type Props = {
  name: string;
  placeholder?: string;
  icon?: IconTypes;
  className?: string;
  iconPosition?: 'left' | 'right' | 'none';
  fieldError?: FieldError | undefined | null;
  rules?: RegisterOptions;
  control?: Control;
  label?: string;
  instructions?: string;
  value?: string; // ISO date string with time
  state?: 'default' | 'display-only';
  onChange?: (value: string) => void;
  validMinutes?: number[];
  // DatePicker specific props
  mode?: 'single'; // Only single mode supported for date-time picker
  numberOfMonths?: number;
  disablePast?: boolean;
  disabledDates?: string[];
  disableFuture?: boolean;
  weekendSelectable?: boolean;
};

const Container = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${(props) => props.theme.SpacingXs};
  transition: color 1s ease, background-color 1s ease;
  position: relative;
  width: 100%;
`;

const InputsContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${(props) => props.theme.SpacingXs};
  align-items: start;
`;

const InputContainer = styled.div`
  height: auto;
  min-height: 37px;
  display: grid;
  align-items: center;
  justify-content: center;
  min-width: 95px;
  cursor: pointer;
  grid-template-columns: 1fr;
  position: relative;
`;

const ActiveZone = styled.div`
  display: grid;
  grid-template-rows: 1fr auto;
  gap: ${(props) => props.theme.SpacingSm};
  align-items: self-start;
  width: 100%;
`;

const DateInputContainer = styled.div<{
  dropdownOpen: boolean;
  state: 'default' | 'display-only';
  error?: boolean;
}>`
  height: 37px;
  overflow: hidden;
  z-index: 8;
  outline: unset;
  text-align: left;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border: 1px solid
    ${(props) =>
      props?.dropdownOpen
        ? props.theme.ColorsStrokesFocus
        : props.error
        ? props.theme.ColorsUtilityColorError
        : props.theme.ColorsStrokesGrey};
  box-sizing: border-box;
  width: 100%;
  padding: ${(props) => props.theme.SpacingSm};
  border-radius: ${(props) => props.theme.RadiusXs};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: grid;
  grid-template-areas: 'left-icon date-display right-icon dropdown-icon';
  grid-template-rows: 1fr;
  grid-template-columns: auto 1fr auto auto;
  align-content: center;

  ${({ state, dropdownOpen }) =>
    state === 'display-only'
      ? !dropdownOpen
        ? css`
            border: 1px solid ${(props) => props.theme.ColorsControllersDefault};
            background: ${(props) => props.theme.ColorsInputsNonEditable};
          `
        : css`
            background: ${(props) => props.theme.ColorsInputsPrimary};
            border: 1px solid ${(props) => props.theme.ColorsStrokesFocus};
          `
      : ''}
`;

const TimeInputContainer = styled.div<{
  dropdownOpen: boolean;
  state: 'default' | 'display-only';
  error?: boolean;
}>`
  height: 37px;
  overflow: hidden;
  z-index: 8;
  outline: unset;
  text-align: left;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border: 1px solid
    ${(props) =>
      props?.dropdownOpen
        ? props.theme.ColorsStrokesFocus
        : props.error
        ? props.theme.ColorsUtilityColorError
        : props.theme.ColorsStrokesGrey};
  box-sizing: border-box;
  width: 100%;
  padding: ${(props) => props.theme.SpacingSm};
  border-radius: ${(props) => props.theme.RadiusXs};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: grid;
  grid-template-areas: 'left-icon time-display right-icon dropdown-icon';
  grid-template-rows: 1fr;
  grid-template-columns: auto 1fr auto auto;
  align-content: center;

  ${({ state, dropdownOpen }) =>
    state === 'display-only'
      ? !dropdownOpen
        ? css`
            border: 1px solid ${(props) => props.theme.ColorsControllersDefault};
            background: ${(props) => props.theme.ColorsInputsNonEditable};
          `
        : css`
            background: ${(props) => props.theme.ColorsInputsPrimary};
            border: 1px solid ${(props) => props.theme.ColorsStrokesFocus};
          `
      : ''}
`;

const DateInput = styled.input<{ dropdownOpen: boolean }>`
  all: unset;
  overflow: hidden;
  outline: unset;
  background-color: transparent;
  text-align: left;
  box-sizing: border-box;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;

  ::placeholder {
    color: ${(props) => props.theme.ColorsTypographyPrimary};
    transition: color 0.3s ease;
  }
`;

const TimeInput = styled.input<{ dropdownOpen: boolean }>`
  all: unset;
  overflow: hidden;
  outline: unset;
  background-color: transparent;
  text-align: left;
  box-sizing: border-box;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;

  ::placeholder {
    color: ${(props) => props.theme.ColorsTypographyPrimary};
    transition: color 0.3s ease;
  }
`;

const DropdownIcon = styled(Icon)`
  grid-area: dropdown-icon;
  padding: 0 ${(props) => props.theme.SpacingXs};
`;

const DropdownMenu = styled.div<{ width: number }>`
  margin: 0 !important;
  position: absolute;
  right: 0px;
  left: 0px;
  border-radius: ${(props) => props.theme.RadiusXs};
  background-color: ${(props) => props.theme.ColorsOverlaySurfaceOverlay};
  border: 1px solid ${(props) => props.theme.ColorsStrokesFocus};
  box-sizing: border-box;
  overflow: hidden;
  display: grid;
  grid-template-rows: auto auto;
  gap: ${(props) => props.theme.SpacingSm};
  justify-items: center;
  text-align: self-start;
  color: ${(props) => props.theme.ColorsIconColorTertiary};
  padding: ${(props) => props.theme.SpacingSm};
  z-index: 1;
  width: 100%;

  div {
    color: ${(props) => props.theme.ColorsTypographyPrimary};
    align-items: center;
    outline: none;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`;

const StyledInstuctionContainer = styled.div`
  margin: unset;
`;

const Instruction = styled.div<{ error?: boolean }>`
  margin-top: ${(props) => props.theme.SpacingXs};
  color: ${({ error, theme }) =>
    error ? theme.ColorsUtilityColorError : theme.ColorsTypographySecondary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize2}px;
`;

const IconWrapper = styled(Icon)`
  display: grid;
  grid-auto-flow: column;
  place-items: center;
  align-self: center;
`;

const Label = styled.label`
  margin-bottom: ${(props) => props.theme.SpacingXs};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
`;

const LeftIconContainer = styled.span<{ position: 'left' | 'right' | 'none' }>`
  grid-area: left-icon;
  padding: 0
    ${(props) => (props?.position === 'left' ? props.theme.SpacingXs : 0)};
`;

const RightIconContainer = styled.span<{ position: 'left' | 'right' | 'none' }>`
  grid-area: right-icon;
  padding: 0
    ${(props) => (props?.position === 'right' ? props.theme.SpacingXs : 0)};
`;

/**
 * A combined date and time picker component with side-by-side inputs that allows users to select both date and time.
 * The component displays separate date and time inputs but manages a single ISO date string value.
 *
 * Features:
 * - Side-by-side date and time inputs for better UX
 * - Single form field integration with react-hook-form
 * - Separate dropdowns for date and time selection
 * - ISO date string value management
 * - Reactive rendering of default values
 *
 * @param {object} props - The component props.
 * @param {string} props.name - The input name for react-hook-form.
 * @param {string} [props.placeholder] - The main placeholder text (not used in side-by-side layout).
 * @param {Control} props.control - The react-hook-form control.
 * @param {RegisterOptions} [props.rules] - Validation rules.
 * @param {FieldError} [props.fieldError] - Field error object.
 * @param {string} [props.label] - The input label.
 * @param {string} [props.instructions] - The input instructions.
 * @param {string} [props.value] - Default ISO date string value.
 * @param {function} [props.onChange] - Callback when value changes.
 * @param {number[]} [props.validMinutes] - Valid minutes for time picker.
 * @param {IconTypes} [props.icon] - Icon to display on date input.
 * @param {'left' | 'right' | 'none'} [props.iconPosition] - Icon position on date input.
 * @param {'default' | 'display-only'} [props.state] - Component state.
 * @param {number} [props.numberOfMonths] - Number of months to display in date picker.
 * @param {boolean} [props.disablePast] - Whether to disable past dates.
 * @param {string[]} [props.disabledDates] - Array of disabled dates.
 * @param {boolean} [props.disableFuture] - Whether to disable future dates.
 * @param {boolean} [props.weekendSelectable] - Whether weekends are selectable.
 * @return {JSX.Element} The date and time picker component with side-by-side inputs.
 */
export const DateAndTimePicker = ({
  name,
  icon,
  iconPosition: position = 'none',
  fieldError,
  rules,
  control,
  label,
  instructions,
  value,
  state = 'default',
  onChange,
  validMinutes = [0, 15, 30, 45],
  // DatePicker props
  mode = 'single',
  numberOfMonths = 1,
  disablePast = true,
  disabledDates = [],
  disableFuture = false,
  weekendSelectable = false,
}: Props) => {
  const [dateDropdownOpen, setDateDropdownOpen] = useState(false);
  const [timeDropdownOpen, setTimeDropdownOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [selectedTime, setSelectedTime] = useState<string>('');

  const dateDropdownRef = useRef<HTMLDivElement>(null);
  const timeDropdownRef = useRef<HTMLDivElement>(null);
  const dateInputContainerRef = useRef<HTMLDivElement>(null);
  const timeInputContainerRef = useRef<HTMLDivElement>(null);

  // Parse initial value
  useEffect(() => {
    if (value) {
      try {
        const date = new Date(value);
        if (!isNaN(date.getTime())) {
          setSelectedDate(date);
          const hours = date.getHours().toString().padStart(2, '0');
          const minutes = date.getMinutes().toString().padStart(2, '0');
          setSelectedTime(`${hours}:${minutes}`);
        }
      } catch (error) {
        console.warn('Invalid date value provided:', value);
      }
    }
  }, [value]);

  useEffect(() => {
    const updateDropdownWidths = () => {
      if (dateInputContainerRef.current && dateDropdownRef.current) {
        const width = dateInputContainerRef.current.offsetWidth;
        dateDropdownRef.current.style.width = `${width}px`;
      }
      if (timeInputContainerRef.current && timeDropdownRef.current) {
        const width = timeInputContainerRef.current.offsetWidth;
        timeDropdownRef.current.style.width = `${width}px`;
      }
    };

    updateDropdownWidths();
    window.addEventListener('resize', updateDropdownWidths);

    const handleClickOutside = (event: MouseEvent) => {
      // Handle date dropdown
      if (
        dateDropdownRef.current &&
        !dateDropdownRef.current.contains(event.target as Node) &&
        dateInputContainerRef.current &&
        !dateInputContainerRef.current.contains(event.target as Node)
      ) {
        setDateDropdownOpen(false);
      }

      // Handle time dropdown
      if (
        timeDropdownRef.current &&
        !timeDropdownRef.current.contains(event.target as Node) &&
        timeInputContainerRef.current &&
        !timeInputContainerRef.current.contains(event.target as Node)
      ) {
        setTimeDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      window.removeEventListener('resize', updateDropdownWidths);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const formatDateDisplay = (date: Date | undefined) => {
    if (!date) return '';
    return date.toDateString();
  };

  const formatTimeDisplay = (time: string) => {
    return time || '';
  };

  const createDateTimeString = (date: Date | undefined, time: string) => {
    if (!date || !time) return '';

    const [hours, minutes] = time.split(':').map(Number);
    const newDate = new Date(date);
    newDate.setHours(hours, minutes, 0, 0);
    return newDate.toISOString();
  };

  const handleDateInputClick = () => {
    setDateDropdownOpen((prev) => !prev);
    setTimeDropdownOpen(false); // Close time dropdown when opening date
  };

  const handleTimeInputClick = () => {
    setTimeDropdownOpen((prev) => !prev);
    setDateDropdownOpen(false); // Close date dropdown when opening time
  };

  const handleDateSelect = (date?: Date | DateRange | Date[]) => {
    if (date && date instanceof Date) {
      setSelectedDate(date);
      setDateDropdownOpen(false); // Close dropdown after selection
    }
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
    setTimeDropdownOpen(false); // Close dropdown after selection
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({
        field: { onChange: onFieldChange, value: fieldValue },
        fieldState: { error = fieldError },
      }) => {
        // Update form value when date or time changes
        useEffect(() => {
          const dateTimeString = createDateTimeString(
            selectedDate,
            selectedTime
          );
          if (dateTimeString && dateTimeString !== fieldValue) {
            onFieldChange(dateTimeString);
            if (onChange) {
              onChange(dateTimeString);
            }
          }
        }, [selectedDate, selectedTime, onFieldChange, fieldValue, onChange]);

        return (
          <Container data-testid="date-time-picker">
            {!!label && <Label>{label}</Label>}
            <InputsContainer>
              {/* Date Input */}
              <InputContainer data-testid="date-picker-wrapper">
                <ActiveZone
                  data-testid="date-picker-active-zone"
                  ref={dateInputContainerRef}
                >
                  <DateInputContainer
                    data-testid="date-picker-container"
                    dropdownOpen={dateDropdownOpen}
                    onClick={handleDateInputClick}
                    state={state}
                    error={!!error?.message}
                  >
                    <LeftIconContainer
                      data-testid="date-picker-left-icon"
                      position={position}
                    >
                      {position === 'left' && !!icon && (
                        <IconWrapper type={icon} width={24} height={24} />
                      )}
                    </LeftIconContainer>
                    <DateInput
                      dropdownOpen={dateDropdownOpen}
                      type="text"
                      placeholder={
                        selectedDate
                          ? formatDateDisplay(selectedDate)
                          : 'Select date'
                      }
                      name={`${name}-date`}
                      readOnly
                    />
                    <RightIconContainer position={position}>
                      {position === 'right' && !!icon && (
                        <IconWrapper type={icon} width={24} height={24} />
                      )}
                    </RightIconContainer>
                    <DropdownIcon
                      type={dateDropdownOpen ? 'chevron-up' : 'chevron-down'}
                    />
                  </DateInputContainer>
                  <div>
                    {dateDropdownOpen && (
                      <DropdownMenu
                        data-testid="date-picker-dropdown"
                        width={dateInputContainerRef.current?.offsetWidth || 0}
                        ref={dateDropdownRef}
                      >
                        <DatePicker
                          data-testid="date-picker"
                          mode={mode}
                          numberOfMonths={numberOfMonths}
                          disablePast={disablePast}
                          disabledDates={disabledDates}
                          disableFuture={disableFuture}
                          weekendSelectable={weekendSelectable}
                          selected={selectedDate}
                          onSelect={handleDateSelect}
                        />
                      </DropdownMenu>
                    )}
                  </div>
                </ActiveZone>
              </InputContainer>

              {/* Time Input */}
              <InputContainer data-testid="time-picker-wrapper">
                <ActiveZone
                  data-testid="time-picker-active-zone"
                  ref={timeInputContainerRef}
                >
                  <TimeInputContainer
                    data-testid="time-picker-container"
                    dropdownOpen={timeDropdownOpen}
                    onClick={handleTimeInputClick}
                    state={state}
                    error={!!error?.message}
                  >
                    <LeftIconContainer
                      data-testid="time-picker-left-icon"
                      position="none"
                    ></LeftIconContainer>
                    <TimeInput
                      dropdownOpen={timeDropdownOpen}
                      type="text"
                      placeholder={
                        selectedTime
                          ? formatTimeDisplay(selectedTime)
                          : 'Select time'
                      }
                      name={`${name}-time`}
                      readOnly
                    />
                    <RightIconContainer position="none"></RightIconContainer>
                    <DropdownIcon
                      type={timeDropdownOpen ? 'chevron-up' : 'chevron-down'}
                    />
                  </TimeInputContainer>
                  <div>
                    {timeDropdownOpen && (
                      <DropdownMenu
                        data-testid="time-picker-dropdown"
                        width={timeInputContainerRef.current?.offsetWidth || 0}
                        ref={timeDropdownRef}
                      >
                        <TimePickerV2
                          data-testid="time-picker"
                          onSelect={handleTimeSelect}
                          selected={selectedTime}
                          validMinutes={validMinutes}
                        />
                      </DropdownMenu>
                    )}
                  </div>
                </ActiveZone>
              </InputContainer>
            </InputsContainer>
            {error ? (
              <Instruction error={!!error?.message}>
                {error?.message}
              </Instruction>
            ) : (
              <StyledInstuctionContainer>
                {instructions && <Instruction>{instructions}</Instruction>}
              </StyledInstuctionContainer>
            )}
          </Container>
        );
      }}
    />
  );
};
